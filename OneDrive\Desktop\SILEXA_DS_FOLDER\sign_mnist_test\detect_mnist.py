import cv2
import mediapipe as mp
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import load_model
from gtts import gTTS
import playsound
import os
import time

# === Load model & labels ===
model = load_model('model.h5')
with open("labels.txt", "r") as f:
    labels = [line.strip() for line in f]

# === Initialize Mediapipe ===
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(max_num_hands=1, min_detection_confidence=0.7)
mp_draw = mp.solutions.drawing_utils

# === Text to Speech ===
def speak_word(text):
    try:
        tts = gTTS(text=text, lang='en')
        filename = "voice.mp3"
        tts.save(filename)
        playsound.playsound(filename)
        os.remove(filename)
    except Exception as e:
        print(f"[TTS Error]: {e}")

# === Preprocess Hand ROI ===
def preprocess_hand_image(image, bbox):
    x, y, w, h = bbox
    margin = 20
    x1, y1 = max(0, x - margin), max(0, y - margin)
    x2, y2 = min(image.shape[1], x + w + margin), min(image.shape[0], y + h + margin)
    roi = image[y1:y2, x1:x2]
    gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
    resized = cv2.resize(gray, (28, 28))
    normalized = resized / 255.0
    return normalized.reshape(1, 28, 28, 1)

# === Initialize Camera & Buffer ===
cap = cv2.VideoCapture(0)
prev_gesture = ""
last_spoken_time = 0
last_letter_time = 0
cooldown = 1.5  # seconds
sentence_buffer = ""

print("🖐️ Starting Real-Time Sentence Builder...")

try:
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame = cv2.flip(frame, 1)
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        result = hands.process(rgb)

        gesture = None

        if result.multi_hand_landmarks:
            for hand_landmarks in result.multi_hand_landmarks:
                mp_draw.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

                h, w, _ = frame.shape
                x_min, y_min = w, h
                x_max, y_max = 0, 0

                for lm in hand_landmarks.landmark:
                    x, y = int(lm.x * w), int(lm.y * h)
                    x_min = min(x_min, x)
                    y_min = min(y_min, y)
                    x_max = max(x_max, x)
                    y_max = max(y_max, y)

                bbox = (x_min, y_min, x_max - x_min, y_max - y_min)
                input_image = preprocess_hand_image(frame, bbox)

                prediction = model.predict(input_image)
                predicted_index = np.argmax(prediction)
                gesture = labels[predicted_index]

                current_time = time.time()
                if gesture != prev_gesture and current_time - last_letter_time > cooldown:
                    sentence_buffer += gesture
                    print(f"🧠 Added: {gesture} → {sentence_buffer}")
                    prev_gesture = gesture
                    last_letter_time = current_time

        else:
            prev_gesture = ""

        # === Display on Frame ===
        cv2.putText(frame, f'Sentence: {sentence_buffer}', (10, 60),
                    cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)

        cv2.putText(frame, 'ENTER: Speak  |  BACKSPACE: Clear  |  ESC: Exit', (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        cv2.imshow("SILEXA - Sentence Builder", frame)

        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            break
        elif key == 13:  # ENTER
            if sentence_buffer:
                print(f"🗣️ Speaking: {sentence_buffer}")
                speak_word(sentence_buffer)
                sentence_buffer = ""
        elif key == 8:  # BACKSPACE
            sentence_buffer = ""
            print("🧹 Sentence cleared.")

except KeyboardInterrupt:
    print("🛑 Interrupted.")

finally:
    print("🧹 Cleaning up...")
    cap.release()
    cv2.destroyAllWindows()
